# Tool Crawl Dữ Liệu Xã Phường API

## Mô tả
Tool này được tạo để crawl dữ liệu xã phường từ API `https://sapnhap.bando.com.vn/ptracuu` và lưu vào bảng `ward` trong database MySQL.

## Cấu trúc Files

### 1. `crawl_xaphuong_api.py`
- **<PERSON><PERSON><PERSON> đích**: Tool cơ bản để crawl dữ liệu một tỉnh thành
- **Class chính**: `XaPhuongCrawler`
- **Chức năng**:
  - Gọi API để lấy dữ liệu xã phường
  - Transform dữ liệu từ format API sang format database
  - Lưu dữ liệu vào bảng `ward`
  - Tạo file backup SQL

### 2. `crawl_all_provinces.py`
- **Mục đích**: Tool nâng cao để crawl nhiều tỉnh thành
- **Chức năng**:
  - Crawl tất cả tỉnh thành
  - Crawl một tỉnh cụ thể
  - Crawl nhiều tỉnh cụ thể
  - Xem danh sách tỉnh thành

## Cách sử dụng

### Tool cơ bản
```bash
# Chạy tool cơ bản (mặc định crawl tỉnh Lào Cai - ID 13)
python3 crawl_xaphuong_api.py
```

### Tool nâng cao
```bash
# Xem hướng dẫn
python3 crawl_all_provinces.py

# Xem danh sách tỉnh thành
python3 crawl_all_provinces.py list

# Crawl một tỉnh cụ thể (ví dụ: Hà Nội - ID 1)
python3 crawl_all_provinces.py single 1

# Crawl nhiều tỉnh cụ thể
python3 crawl_all_provinces.py multiple 1,13,29

# Crawl tất cả tỉnh thành (cẩn thận!)
python3 crawl_all_provinces.py all
```

## Cấu trúc dữ liệu

### API Response Format
```json
{
    "id": 408,
    "matinh": 13,
    "ma": "0408",
    "tentinh": "tỉnh Lào Cai",
    "loai": "xã",
    "tenhc": "Yên Bình",
    "cay": "0310.0408",
    "dientichkm2": 9,
    "dansonguoi": "653422",
    "trungtamhc": "đang cập nhật",
    "kinhdo": 104.393,
    "vido": 22.036,
    "truocsapnhap": "Thị trấn Yên Bình, Xã Tân Hương...",
    "maxa": -1
}
```

### Database Mapping
| API Field | Database Field | Mô tả |
|-----------|----------------|-------|
| `id` | `pti_id` | ID từ API làm PTI ID |
| `matinh` | `province_id` | ID tỉnh thành |
| `loai` | `prefix` | Loại: xã, phường, thị trấn |
| `tenhc` | `title` | Tên xã phường |
| - | `title_full` | Tên đầy đủ: "{loai} {tenhc}, {tentinh}" |
| - | `is_merge` | Luôn = 2 (dữ liệu mới) |
| - | `district_id` | Luôn = 0 (không dùng) |

## Cấu hình Database

### Thông tin kết nối
- **Host**: localhost
- **Port**: 3306
- **User**: root
- **Password**: root
- **Database**: urbox

### Bảng nguồn dữ liệu
- **Bảng tỉnh thành**: `tinhthanh`
- **Field ID**: `mahc` (Mã hành chính)
- **Field tên**: `tentinh`

### Bảng `ward`
```sql
CREATE TABLE `ward` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `province_id` int DEFAULT '0',
  `district_id` int unsigned DEFAULT '0',
  `pti_id` int DEFAULT '0' COMMENT 'Mã phường của PTI',
  `prefix` varchar(20) DEFAULT '',
  `title` varchar(50) NOT NULL DEFAULT '',
  `title_full` varchar(255) DEFAULT '',
  `status` tinyint(1) DEFAULT '2',
  `created_at` int NOT NULL DEFAULT '0',
  `updated_at` int NOT NULL DEFAULT '0',
  `created_by` varchar(255) NOT NULL DEFAULT 'system',
  `updated_by` varchar(255) DEFAULT NULL,
  `deleted_at` int DEFAULT '0',
  `deleted_by` varchar(255) DEFAULT NULL,
  `is_merge` int DEFAULT '2',
  PRIMARY KEY (`id`)
);
```

## Kiểm tra dữ liệu

### Kiểm tra tổng số records
```sql
SELECT COUNT(*) as total FROM ward WHERE is_merge = 2;
```

### Kiểm tra theo tỉnh
```sql
SELECT province_id, COUNT(*) as total
FROM ward
WHERE is_merge = 2
GROUP BY province_id
ORDER BY province_id;
```

### Kiểm tra dữ liệu mẫu
```sql
-- Kiểm tra dữ liệu Hà Nội (mahc = 1)
SELECT id, province_id, pti_id, prefix, title, title_full
FROM ward
WHERE province_id = 1 AND is_merge = 2
LIMIT 5;

-- Kiểm tra dữ liệu Điện Biên (mahc = 13)
SELECT id, province_id, pti_id, prefix, title, title_full
FROM ward
WHERE province_id = 13 AND is_merge = 2
LIMIT 5;
```

## Lưu ý quan trọng

### 1. Backup tự động
- Tool tự động tạo file backup SQL với format: `crawl_ward_backup_YYYYMMDD_HHMMSS.sql`
- File backup chứa tất cả câu lệnh INSERT để có thể khôi phục nếu cần

### 2. Rate limiting
- Tool có delay 2 giây giữa các request để tránh spam API
- Không nên crawl quá nhiều tỉnh cùng lúc

### 3. Error handling
- Tool có xử lý lỗi và báo cáo chi tiết
- Nếu một tỉnh thất bại, tool vẫn tiếp tục với tỉnh khác

### 4. Dữ liệu trùng lặp
- Tool không kiểm tra trùng lặp, có thể tạo duplicate records
- Nên kiểm tra và xóa dữ liệu cũ trước khi crawl lại

## Troubleshooting

### Lỗi kết nối database
```bash
# Kiểm tra MySQL service
brew services list | grep mysql

# Khởi động MySQL nếu cần
brew services start mysql
```

### Lỗi API timeout
- Kiểm tra kết nối internet
- Thử lại với tỉnh khác
- Tăng timeout trong code nếu cần

### Lỗi encoding
- Tool đã xử lý UTF-8 encoding
- Nếu vẫn lỗi, kiểm tra charset của database

## Ví dụ sử dụng thực tế

### Test với một tỉnh
```bash
# Test với Hà Nội (mahc = 1)
python3 crawl_all_provinces.py single 1
```

### Crawl một số tỉnh lớn
```bash
# Crawl Hà Nội, TP.HCM, Đà Nẵng (mahc = 1, 29, 21)
python3 crawl_all_provinces.py multiple 1,29,21
```

### Kiểm tra kết quả
```sql
-- Xem tổng số xã phường đã crawl
SELECT 
    p.title as province_name,
    COUNT(w.id) as ward_count
FROM ward w
JOIN ___province p ON w.province_id = p.id
WHERE w.is_merge = 2
GROUP BY w.province_id, p.title
ORDER BY ward_count DESC;
```
