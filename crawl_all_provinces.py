#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import time
from crawl_xaphuong_api import XaPhuongCrawler

def get_province_list():
    """Lấy danh sách các tỉnh thành từ database"""
    import subprocess
    
    try:
        cmd = [
            'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
            '-D', 'urbox', '--batch', '--raw',
            '-e', 'SELECT id, title FROM ___province WHERE is_megre = 2 ORDER BY id'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            print(f"❌ Lỗi MySQL: {result.stderr}")
            return []
        
        provinces = []
        lines = result.stdout.strip().split('\n')[1:]  # Bỏ header
        for line in lines:
            parts = line.split('\t')
            if len(parts) >= 2:
                province_id = int(parts[0])
                title = parts[1].strip()
                provinces.append({
                    'id': province_id,
                    'title': title
                })
        
        print(f"✅ Đã load {len(provinces)} tỉnh thành")
        return provinces
        
    except Exception as e:
        print(f"❌ Lỗi khi lấy danh sách tỉnh: {e}")
        return []

def crawl_all_provinces():
    """Crawl tất cả các tỉnh thành"""
    print("🚀 BẮT ĐẦU CRAWL TẤT CẢ TỈNH THÀNH")
    print("=" * 60)
    
    # Lấy danh sách tỉnh
    provinces = get_province_list()
    if not provinces:
        print("❌ Không có dữ liệu tỉnh thành")
        return
    
    crawler = XaPhuongCrawler()
    
    success_count = 0
    failed_provinces = []
    
    for i, province in enumerate(provinces):
        province_id = province['id']
        province_title = province['title']
        
        print(f"\n📍 [{i+1}/{len(provinces)}] Crawling {province_title} (ID: {province_id})")
        print("-" * 50)
        
        try:
            success = crawler.crawl_and_save(province_id)
            if success:
                success_count += 1
                print(f"✅ Thành công: {province_title}")
            else:
                failed_provinces.append(province)
                print(f"❌ Thất bại: {province_title}")
                
        except Exception as e:
            failed_provinces.append(province)
            print(f"❌ Exception: {province_title} - {e}")
        
        # Nghỉ 2 giây giữa các request để tránh spam
        if i < len(provinces) - 1:
            print("⏳ Nghỉ 2 giây...")
            time.sleep(2)
    
    # Báo cáo kết quả
    print("\n" + "=" * 60)
    print("📊 BÁO CÁO KẾT QUẢ")
    print("=" * 60)
    print(f"✅ Thành công: {success_count}/{len(provinces)} tỉnh")
    print(f"❌ Thất bại: {len(failed_provinces)} tỉnh")
    
    if failed_provinces:
        print("\n❌ Các tỉnh thất bại:")
        for province in failed_provinces:
            print(f"   - {province['title']} (ID: {province['id']})")
    
    print(f"\n💡 Kiểm tra tổng số records:")
    print(f"   SELECT province_id, COUNT(*) as total FROM ward WHERE is_merge = 2 GROUP BY province_id ORDER BY province_id;")

def crawl_specific_provinces(province_ids):
    """Crawl các tỉnh cụ thể"""
    print(f"🚀 CRAWL CÁC TỈNH CỤ THỂ: {province_ids}")
    print("=" * 60)
    
    crawler = XaPhuongCrawler()
    
    success_count = 0
    failed_ids = []
    
    for i, province_id in enumerate(province_ids):
        print(f"\n📍 [{i+1}/{len(province_ids)}] Crawling tỉnh ID: {province_id}")
        print("-" * 50)
        
        try:
            success = crawler.crawl_and_save(province_id)
            if success:
                success_count += 1
                print(f"✅ Thành công: ID {province_id}")
            else:
                failed_ids.append(province_id)
                print(f"❌ Thất bại: ID {province_id}")
                
        except Exception as e:
            failed_ids.append(province_id)
            print(f"❌ Exception: ID {province_id} - {e}")
        
        # Nghỉ 2 giây giữa các request
        if i < len(province_ids) - 1:
            print("⏳ Nghỉ 2 giây...")
            time.sleep(2)
    
    # Báo cáo kết quả
    print("\n" + "=" * 60)
    print("📊 BÁO CÁO KẾT QUẢ")
    print("=" * 60)
    print(f"✅ Thành công: {success_count}/{len(province_ids)} tỉnh")
    print(f"❌ Thất bại: {len(failed_ids)} tỉnh")
    
    if failed_ids:
        print(f"\n❌ Các tỉnh thất bại: {failed_ids}")

def main():
    """Hàm main"""
    if len(sys.argv) < 2:
        print("🔧 CRAWL XÃ PHƯỜNG API TOOL - ADVANCED")
        print("=" * 60)
        print("Cách sử dụng:")
        print("  python3 crawl_all_provinces.py all                    # Crawl tất cả tỉnh")
        print("  python3 crawl_all_provinces.py single <id>            # Crawl 1 tỉnh")
        print("  python3 crawl_all_provinces.py multiple <id1,id2,...> # Crawl nhiều tỉnh")
        print("  python3 crawl_all_provinces.py list                   # Xem danh sách tỉnh")
        print("\nVí dụ:")
        print("  python3 crawl_all_provinces.py single 13")
        print("  python3 crawl_all_provinces.py multiple 13,14,15")
        return
    
    command = sys.argv[1].lower()
    
    if command == "all":
        crawl_all_provinces()
        
    elif command == "single":
        if len(sys.argv) < 3:
            print("❌ Thiếu province ID")
            return
        try:
            province_id = int(sys.argv[2])
            crawl_specific_provinces([province_id])
        except ValueError:
            print("❌ Province ID phải là số")
            
    elif command == "multiple":
        if len(sys.argv) < 3:
            print("❌ Thiếu danh sách province IDs")
            return
        try:
            province_ids = [int(x.strip()) for x in sys.argv[2].split(',')]
            crawl_specific_provinces(province_ids)
        except ValueError:
            print("❌ Province IDs phải là các số, cách nhau bởi dấu phẩy")
            
    elif command == "list":
        provinces = get_province_list()
        if provinces:
            print("📋 DANH SÁCH TỈNH THÀNH:")
            print("=" * 60)
            for province in provinces:
                print(f"ID: {province['id']:2d} - {province['title']}")
        
    else:
        print(f"❌ Lệnh không hợp lệ: {command}")

if __name__ == "__main__":
    main()
