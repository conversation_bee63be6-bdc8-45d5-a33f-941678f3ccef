#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.request
import urllib.parse
import json
import time
import subprocess
from datetime import datetime

class XaPhuongCrawler:
    def __init__(self):
        self.base_url = "https://sapnhap.bando.com.vn/ptracuu"
        self.headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Cookie': 'PHPSESSID=tncekeavtrlr9jtequvls32mtl',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def crawl_province_data(self, province_id):
        """Crawl dữ liệu xã phường của một tỉnh"""
        print(f"🔄 Crawling dữ liệu tỉnh ID: {province_id}")

        try:
            # Tạo POST data
            data = urllib.parse.urlencode({'id': str(province_id)}).encode('utf-8')

            # Tạo request
            req = urllib.request.Request(
                self.base_url,
                data=data,
                headers=self.headers
            )

            # Gửi request
            with urllib.request.urlopen(req, timeout=30) as response:
                if response.getcode() != 200:
                    print(f"❌ HTTP Error: {response.getcode()}")
                    return None

                # Đọc response
                response_data = response.read().decode('utf-8')

                # Parse JSON
                try:
                    json_data = json.loads(response_data)
                    if not isinstance(json_data, list):
                        print(f"❌ Response không phải là array: {type(json_data)}")
                        return None

                    print(f"✅ Crawl thành công {len(json_data)} records")
                    return json_data

                except json.JSONDecodeError as e:
                    print(f"❌ Lỗi parse JSON: {e}")
                    print(f"Response text: {response_data[:500]}")
                    return None

        except Exception as e:
            print(f"❌ Lỗi request: {e}")
            return None
    
    def transform_data(self, raw_data, province_id):
        """Chuyển đổi dữ liệu từ API sang format phù hợp với bảng xaphuong"""
        transformed_data = []

        for item in raw_data:
            try:
                # Extract dữ liệu từ response - khớp hoàn toàn với cấu trúc bảng xaphuong
                xaphuong_record = {
                    'matinh': item.get('matinh', province_id),
                    'ma': item.get('ma', ''),
                    'tentinh': item.get('tentinh', ''),
                    'loai': item.get('loai', ''),
                    'tenhc': item.get('tenhc', ''),
                    'cay': item.get('cay', ''),
                    'dientichkm2': item.get('dientichkm2', None),
                    'dansonguoi': item.get('dansonguoi', ''),
                    'trungtamhc': item.get('trungtamhc', ''),
                    'kinhdo': item.get('kinhdo', None),
                    'vido': item.get('vido', None),
                    'truocsapnhap': item.get('truocsapnhap', ''),
                    'maxa': item.get('maxa', None),
                    'geo_data': None  # Có thể lưu thông tin bổ sung sau
                }

                transformed_data.append(xaphuong_record)

            except Exception as e:
                print(f"❌ Lỗi transform record {item.get('id', 'unknown')}: {e}")
                continue

        print(f"✅ Transform thành công {len(transformed_data)} records")
        return transformed_data
    
    def save_to_database(self, xaphuong_data):
        """Lưu dữ liệu vào bảng xaphuong"""
        if not xaphuong_data:
            print("❌ Không có dữ liệu để lưu")
            return False

        print(f"💾 Lưu {len(xaphuong_data)} records vào bảng xaphuong...")

        try:
            # Tạo các câu lệnh INSERT
            insert_statements = []

            for record in xaphuong_data:
                # Escape single quotes trong string
                def escape_sql(value):
                    if value is None:
                        return 'NULL'
                    if isinstance(value, str):
                        return f"'{value.replace(chr(39), chr(39)+chr(39))}'"
                    return str(value)

                insert_sql = f"""INSERT INTO xaphuong (
                    matinh, ma, tentinh, loai, tenhc, cay,
                    dientichkm2, dansonguoi, trungtamhc, kinhdo, vido,
                    truocsapnhap, maxa, geo_data
                ) VALUES (
                    {record['matinh']}, {escape_sql(record['ma'])}, {escape_sql(record['tentinh'])},
                    {escape_sql(record['loai'])}, {escape_sql(record['tenhc'])}, {escape_sql(record['cay'])},
                    {record['dientichkm2']}, {escape_sql(record['dansonguoi'])}, {escape_sql(record['trungtamhc'])},
                    {record['kinhdo']}, {record['vido']}, {escape_sql(record['truocsapnhap'])},
                    {record['maxa']}, {escape_sql(record['geo_data'])}
                )"""

                insert_statements.append(insert_sql)
            
            # Ghi ra file SQL backup
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"crawl_xaphuong_backup_{timestamp}.sql"

            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(f'-- Crawl xaphuong data backup - {datetime.now()}\n')
                f.write(f'-- Total records: {len(insert_statements)}\n\n')

                for i, stmt in enumerate(insert_statements):
                    f.write(f'-- Record {i+1}\n')
                    f.write(stmt + ';\n\n')

            print(f"💾 Đã tạo file backup: {backup_file}")
            
            # Thực thi insert vào database
            success_count = 0
            for i, stmt in enumerate(insert_statements):
                try:
                    cmd = [
                        'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                        '-D', 'urbox', '-e', stmt
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        success_count += 1
                    else:
                        print(f"❌ Lỗi insert record {i+1}: {result.stderr}")
                        
                except Exception as e:
                    print(f"❌ Exception insert record {i+1}: {e}")
                    
                # Progress indicator
                if (i + 1) % 10 == 0:
                    print(f"📝 Đã xử lý {i+1}/{len(insert_statements)} records...")
            
            print(f"✅ Insert thành công {success_count}/{len(insert_statements)} records")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ Lỗi save database: {e}")
            return False
    
    def crawl_and_save(self, province_id):
        """Crawl và lưu dữ liệu cho một tỉnh"""
        print(f"🚀 Bắt đầu crawl tỉnh ID: {province_id}")
        
        # 1. Crawl dữ liệu
        raw_data = self.crawl_province_data(province_id)
        if not raw_data:
            return False
        
        # 2. Transform dữ liệu
        xaphuong_data = self.transform_data(raw_data, province_id)
        if not xaphuong_data:
            return False

        # 3. Lưu vào bảng xaphuong
        success = self.save_to_database(xaphuong_data)
        
        if success:
            print(f"🎉 Hoàn thành crawl tỉnh ID: {province_id}")
        else:
            print(f"❌ Thất bại crawl tỉnh ID: {province_id}")
            
        return success

def main():
    """Hàm main để test"""
    crawler = XaPhuongCrawler()
    
    # Test với tỉnh Lào Cai (ID = 13)
    test_province_id = 13
    
    print("=" * 60)
    print("🔧 CRAWL XÃ PHƯỜNG API TOOL")
    print("=" * 60)
    
    success = crawler.crawl_and_save(test_province_id)
    
    if success:
        print("\n✅ CRAWL THÀNH CÔNG!")
        print("💡 Kiểm tra dữ liệu bằng lệnh:")
        print(f"   SELECT COUNT(*) FROM xaphuong WHERE matinh = {test_province_id};")
    else:
        print("\n❌ CRAWL THẤT BẠI!")

if __name__ == "__main__":
    main()
