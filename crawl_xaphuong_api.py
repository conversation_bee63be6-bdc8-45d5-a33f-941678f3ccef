#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import urllib.request
import urllib.parse
import json
import time
import subprocess
from datetime import datetime

class XaPhuongCrawler:
    def __init__(self):
        self.base_url = "https://sapnhap.bando.com.vn/ptracuu"
        self.headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Cookie': 'PHPSESSID=tncekeavtrlr9jtequvls32mtl',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
    def crawl_province_data(self, province_id):
        """Crawl dữ liệu xã phường của một tỉnh"""
        print(f"🔄 Crawling dữ liệu tỉnh ID: {province_id}")

        try:
            # Tạo POST data
            data = urllib.parse.urlencode({'id': str(province_id)}).encode('utf-8')

            # Tạo request
            req = urllib.request.Request(
                self.base_url,
                data=data,
                headers=self.headers
            )

            # Gửi request
            with urllib.request.urlopen(req, timeout=30) as response:
                if response.getcode() != 200:
                    print(f"❌ HTTP Error: {response.getcode()}")
                    return None

                # Đọc response
                response_data = response.read().decode('utf-8')

                # Parse JSON
                try:
                    json_data = json.loads(response_data)
                    if not isinstance(json_data, list):
                        print(f"❌ Response không phải là array: {type(json_data)}")
                        return None

                    print(f"✅ Crawl thành công {len(json_data)} records")
                    return json_data

                except json.JSONDecodeError as e:
                    print(f"❌ Lỗi parse JSON: {e}")
                    print(f"Response text: {response_data[:500]}")
                    return None

        except Exception as e:
            print(f"❌ Lỗi request: {e}")
            return None
    
    def transform_data(self, raw_data, province_id):
        """Chuyển đổi dữ liệu từ API sang format phù hợp với bảng ward"""
        transformed_data = []
        current_time = int(time.time())
        
        for item in raw_data:
            try:
                # Extract dữ liệu từ response
                ward_id = item.get('id', 0)
                ma_tinh = item.get('matinh', province_id)
                ten_tinh = item.get('tentinh', '')
                loai = item.get('loai', '')
                ten_hc = item.get('tenhc', '')
                
                # Tạo record cho bảng ward
                ward_record = {
                    'province_id': ma_tinh,
                    'district_id': 0,  # Không dùng district nữa
                    'pti_id': ward_id,  # Dùng ID từ API làm pti_id
                    'prefix': loai,     # xã, phường, thị trấn
                    'title': ten_hc,    # Tên xã phường
                    'title_full': f"{loai} {ten_hc}, {ten_tinh}",
                    'status': 2,        # Active
                    'created_at': current_time,
                    'updated_at': current_time,
                    'created_by': 'system:crawl_api',
                    'updated_by': 'system:crawl_api',
                    'deleted_at': 0,
                    'deleted_by': None,
                    'is_merge': 2       # Dữ liệu mới sau sáp nhập
                }
                
                transformed_data.append(ward_record)
                
            except Exception as e:
                print(f"❌ Lỗi transform record {item.get('id', 'unknown')}: {e}")
                continue
        
        print(f"✅ Transform thành công {len(transformed_data)} records")
        return transformed_data
    
    def save_to_database(self, ward_data):
        """Lưu dữ liệu vào bảng ward"""
        if not ward_data:
            print("❌ Không có dữ liệu để lưu")
            return False
            
        print(f"💾 Lưu {len(ward_data)} records vào database...")
        
        try:
            # Tạo các câu lệnh INSERT
            insert_statements = []
            
            for record in ward_data:
                # Escape single quotes trong string
                def escape_sql(value):
                    if value is None:
                        return 'NULL'
                    if isinstance(value, str):
                        return f"'{value.replace(chr(39), chr(39)+chr(39))}'"
                    return str(value)
                
                insert_sql = f"""INSERT INTO ward (
                    province_id, district_id, pti_id, prefix, title, title_full,
                    status, created_at, updated_at, created_by, updated_by,
                    deleted_at, deleted_by, is_merge
                ) VALUES (
                    {record['province_id']}, {record['district_id']}, {record['pti_id']},
                    {escape_sql(record['prefix'])}, {escape_sql(record['title'])}, 
                    {escape_sql(record['title_full'])}, {record['status']},
                    {record['created_at']}, {record['updated_at']},
                    {escape_sql(record['created_by'])}, {escape_sql(record['updated_by'])},
                    {record['deleted_at']}, {escape_sql(record['deleted_by'])}, {record['is_merge']}
                )"""
                
                insert_statements.append(insert_sql)
            
            # Ghi ra file SQL backup
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"crawl_ward_backup_{timestamp}.sql"
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(f'-- Crawl ward data backup - {datetime.now()}\n')
                f.write(f'-- Total records: {len(insert_statements)}\n\n')
                
                for i, stmt in enumerate(insert_statements):
                    f.write(f'-- Record {i+1}\n')
                    f.write(stmt + ';\n\n')
            
            print(f"💾 Đã tạo file backup: {backup_file}")
            
            # Thực thi insert vào database
            success_count = 0
            for i, stmt in enumerate(insert_statements):
                try:
                    cmd = [
                        'mysql', '-u', 'root', '-proot', '-h', 'localhost', '-P', '3306',
                        '-D', 'urbox', '-e', stmt
                    ]
                    
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        success_count += 1
                    else:
                        print(f"❌ Lỗi insert record {i+1}: {result.stderr}")
                        
                except Exception as e:
                    print(f"❌ Exception insert record {i+1}: {e}")
                    
                # Progress indicator
                if (i + 1) % 10 == 0:
                    print(f"📝 Đã xử lý {i+1}/{len(insert_statements)} records...")
            
            print(f"✅ Insert thành công {success_count}/{len(insert_statements)} records")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ Lỗi save database: {e}")
            return False
    
    def crawl_and_save(self, province_id):
        """Crawl và lưu dữ liệu cho một tỉnh"""
        print(f"🚀 Bắt đầu crawl tỉnh ID: {province_id}")
        
        # 1. Crawl dữ liệu
        raw_data = self.crawl_province_data(province_id)
        if not raw_data:
            return False
        
        # 2. Transform dữ liệu
        ward_data = self.transform_data(raw_data, province_id)
        if not ward_data:
            return False
        
        # 3. Lưu vào database
        success = self.save_to_database(ward_data)
        
        if success:
            print(f"🎉 Hoàn thành crawl tỉnh ID: {province_id}")
        else:
            print(f"❌ Thất bại crawl tỉnh ID: {province_id}")
            
        return success

def main():
    """Hàm main để test"""
    crawler = XaPhuongCrawler()
    
    # Test với tỉnh Lào Cai (ID = 13)
    test_province_id = 13
    
    print("=" * 60)
    print("🔧 CRAWL XÃ PHƯỜNG API TOOL")
    print("=" * 60)
    
    success = crawler.crawl_and_save(test_province_id)
    
    if success:
        print("\n✅ CRAWL THÀNH CÔNG!")
        print("💡 Kiểm tra dữ liệu bằng lệnh:")
        print(f"   SELECT COUNT(*) FROM ward WHERE province_id = {test_province_id} AND is_merge = 2;")
    else:
        print("\n❌ CRAWL THẤT BẠI!")

if __name__ == "__main__":
    main()
